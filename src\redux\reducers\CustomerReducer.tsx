import { createSlice, PayloadAction, Dispatch } from '@reduxjs/toolkit';
import { useNavigation } from '@react-navigation/native';
import { BaseDA } from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import { DataController } from '../../base/baseController';
import {
  clearDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import store, { RootState } from '../store/store';
import { navigateReset, RootScreen } from '../../router/router';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import { getFcmToken } from '../../features/notifications/fcm/fcm_helper';
import { CustomerStatus, StorageContanst } from '../../Config/Contanst';
import { randomGID } from '../../utils/Utils';
import ShopDA from '../../modules/shop/da';
import { handleActionsShop, ProductActions } from './ShoptReducer';

interface CustomerSimpleResponse {
  data?: any;
  myAddress?: Array<any>;
  onLoading: boolean;
  type?: string;
}

const initState: CustomerSimpleResponse = {
  data: undefined,
  myAddress: [],
  onLoading: false,
};

export const customerSlice = createSlice({
  name: 'customer',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFOR':
          state.data = action.payload.data;
          break;
        case 'GETMYADDRESS':
          state.myAddress = action.payload.data;
          break;
        case 'ADDADDRESS':
          if (
            state.myAddress &&
            state.myAddress.length > 0 &&
            state.myAddress.find(el => el.Id === action.payload.data.Id)
          ) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return el;
            });
          } else {
            if (state.myAddress && state.myAddress.length == 0) {
              action.payload.data.IsDefault = true;
            }
            state.myAddress = [action.payload.data, ...(state.myAddress || [])];
          }
          if (action.payload.data.IsDefault) {
            state.myAddress = state.myAddress.map(el => {
              if (el.Id === action.payload.data.Id) {
                return action.payload.data;
              }
              return {
                ...el,
                IsDefault: false,
              };
            });
          }
          break;
        case 'DELETEADDRESS':
          if (state.myAddress) {
            state.myAddress = state.myAddress.filter(
              el => el.Id !== action.payload.data,
            );
          }
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        case 'LOGOUT':
          state.data = undefined;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.onLoading = true;
    },
  },
});

const { handleActions, onFetching } = customerSlice.actions;

export default customerSlice.reducer;

export class CustomerActions {
  static login = async (body: {
    type: 'phone' | 'apple' | 'google' | 'microsoft' | 'account';
    token?: string;
    deviceToken?: string;
    ggClientId?: string;
    phone?: string;
    password?: string;
    email?: string;
  }) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/login', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: body,
    });
    if (res.code === 200) {
      saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
      saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
      saveDataToAsyncStorage(
        'timeRefresh',
        `${(Date.now() / 1000 + 9 * 60).toString()}`,
      );
    }
    return res;
  };

  static lockAccount = async (phone: string) => {
    const controller = new DataController('Customer');
    const res = (await controller.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:{${phone}}`,
    })) as any;
    if (res.data.length) {
      await controller.edit([{ ...res.data[0], Status: CustomerStatus.locked }]);
      return {
        code: 200,
        message:
          'Tài khoản đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
      };
    } else return { code: 400, message: 'Tài khoản không tồn tại' };
  };

  static getInfor = () => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const shopDA = new ShopDA();
    const res = await BaseDA.get(ConfigAPI.url + 'data/getInfo', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
    });
    if (res.code === 200) {
      const controller = new DataController('Customer');
      const rankController = new DataController('ConfigRank');
      await saveDataToAsyncStorage(StorageContanst.CustomerId, res.data.Id);
      const shop = await shopDA.getShop(res.data.Id);
      if (shop.code === 200) {
        console.log('shop', shop.data);
        dispatch(
          handleActionsShop({
            type: 'GETINFORSHOP',
            data: shop.data,
          }),
        );
      }
      const deviceToken = await getDataToAsyncStorage('fcmToken');
      // EDIT DEVICE TOKEN
      if (!res.data.DeviceToken?.includes(deviceToken) && deviceToken) {
        res.data.DeviceToken ??= '';
        res.data.DeviceToken += `,${deviceToken}`;
        res.data.DeviceToken = res.data.DeviceToken.split(',').filter(
          (devTk: string, i: number, arr: Array<string>) =>
            devTk.length && arr.indexOf(devTk) === i,
        );
        if (res.data.DeviceToken.length > 3) {
          res.data.DeviceToken = res.data.DeviceToken.slice(
            res.data.DeviceToken.length - 3,
          );
        }
        res.data.DeviceToken = res.data.DeviceToken.join(',');
        await controller.edit([res.data]);
      }
      const ranks = await rankController.getAll();
      if (ranks.code === 200) {
        if (ranks.data.length > 0) {
          // kiểm tra rank của res.data trong list ranks, nếu rank =0 hoặc nếu rank khoảng 0-30 thì return, nếu rank khoảng 30-60 thì return, nếu 60-100 thì return, 0,30,60,100 là các string lấy từ field Score
          // lấy file icon
          const file = await BaseDA.getFilesInfor(
            ranks.data.map((e: any) => e.Icon),
          );
          if (file.code === 200) {
            ranks.data = ranks.data.map((e: any) => {
              const _file = file.data.find((f: any) => f.Id === e.Icon);
              return {
                ...e,
                Icon: _file?.Url,
              };
            });
          }
          //
          const rank = ranks.data.find((e: any) => {
            if (res.data.Rank >= parseFloat(e.Score)) {
              return e;
            } else {
              return undefined;
            }
          });
          res.data.RankInfor = rank;
          res.data.RanksData = ranks.data.sort((a: any, b: any) => {
            return parseFloat(a.Score) - parseFloat(b.Score);
          });
        }
      }
      dispatch(
        handleActions({
          type: 'GETINFOR',
          data: res.data,
        }),
      );
    } else if (res.code === 400) {
      // Het token
      removeDataToAsyncStorage('timeRefresh');
      removeDataToAsyncStorage('accessToken');
      removeDataToAsyncStorage('refreshToken');
    }
  };

  static checkPassword = async (phone: string, password?: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/checkPassword', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { phone: phone, password: password },
    });
    return res;
  };

  static hashPassword = async (password?: string) => {
    const res = await BaseDA.get(
      ConfigAPI.url + `data/bcrypt?password=${password}`,
      {
        headers: { module: 'Customer', pid: ConfigAPI.pid },
      },
    );
    return res;
  };

  static logout = () => async (dispatch: Dispatch) => {
    const deviceToken = await getDataToAsyncStorage('fcmToken');
    // Get customer data from parameters instead of store
    const customerController = new DataController('Customer');
    const user = store.getState().customer.data;
    if (!user) {
      navigateReset(RootScreen.login);
      dispatch(
        handleActions({
          type: 'LOGOUT',
        }),
      );
      return;
    }

    if (deviceToken && user?.DeviceToken?.includes(deviceToken)) {
      customerController.edit([
        {
          ...user,
          DeviceToken: user?.DeviceToken
            ? user.DeviceToken?.split(',')
              .filter((e: any) => e.length && e !== deviceToken)
              .join(',')
            : undefined,
        },
      ]);
    }
    // const res = await customerController.edit([
    //   {...user, DeviceToken: undefined},
    // ]);
    // if (res.code == 200) {
    await removeDataToAsyncStorage('accessToken');
    await removeDataToAsyncStorage('refreshToken');
    await removeDataToAsyncStorage('timeRefresh');
    navigateReset(RootScreen.login);
    // NotificationActions.reset(store.dispatch);
    dispatch(
      handleActions({
        type: 'LOGOUT',
      }),
    );
    // }
  };

  static edit = (user: any) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.edit([user]);
    if (res.code === 200) {
      showSnackbar({
        message: 'Cập nhật thông tin tài khoản thành công',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(
        handleActions({
          type: 'UPDATE',
          data: user,
        }),
      );
    }
    return res;
  };

  static getAddresses = (cusId: any) => async (dispatch: Dispatch) => {
    if (!cusId) return;
    dispatch(onFetching());
    const controller = new DataController('Address');
    const res = await controller.getListSimple({
      page: 1,
      size: 10,
      query: `@CustomerId: {${cusId}}`,
    });
    if (res.code === 200) {
      dispatch(
        handleActions({
          type: 'GETMYADDRESS',
          data: res.data,
        }),
      );
    }
    return res;
  };

  static editAddress =
    (address: any, isAdd?: boolean) => async (dispatch: Dispatch) => {
      dispatch(onFetching());
      const controller = new DataController('Address');
      if (address?.IsDefault) {
        const resCheck = await controller.getListSimple({
          page: 1,
          size: 1,
          query: `@CustomerId: {${address.CustomerId}} @IsDefault: {true}`,
        });
        if (resCheck.code === 200) {
          if (resCheck.data.length) {
            await controller.edit([{ ...resCheck.data[0], IsDefault: false }]);
          }
        }
      }
      const res = isAdd
        ? await controller.add([address])
        : await controller.edit([address]);
      if (res.code === 200) {
        showSnackbar({
          message: 'Thao tác thành công',
          status: ComponentStatus.SUCCSESS,
        });
        dispatch(
          handleActions({
            type: 'ADDADDRESS',
            data: address,
          }),
        );
      }
      return res;
    };

  static deleteAddress = (addressId: any) => async (dispatch: Dispatch) => {
    const controller = new DataController('Address');
    const res = await controller.delete([addressId]);
    if (res.code === 200) {
      showSnackbar({
        message: 'Thao tác thành công',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(
        handleActions({
          type: 'DELETEADDRESS',
          data: addressId,
        }),
      );
    }
    return res;
  };

  static delete = async (
    dispatch: Dispatch,
    userId: string,
    navigation: any,
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Customer');
    const res = await controller.delete([userId]);
    if (res.code === 200) {
      clearDataToAsyncStorage();
      getFcmToken();
      showSnackbar({
        message:
          'Tài khoản đã bị xóa khỏi hệ thống, vui lòng đăng nhập lại để sử dụng',
        status: ComponentStatus.WARNING,
      });
      navigation.reset({
        index: 0,
        routes: [{ name: RootScreen.login }],
      });
    }
  };
  //tạo action cho việc cập nhật rank và lưu vào lịch sử
  static updateRank =
    (rank: number, gameId: string) => async (dispatch: Dispatch) => {
      const controller = new DataController('Customer');
      const customer = store.getState().customer;
      const res = await controller.edit([
        { ...customer?.data, Rank: (customer?.data?.Rank ?? 0) + rank },
      ]);
      if (res.code === 200) {
        // lưu vào bảng HistoryGame
        const historyController = new DataController('HistoryScore');
        const data = {
          Id: randomGID(),
          CustomerId: customer?.data.Id,
          GameId: gameId,
          Score: rank,
          Name: customer?.data?.Name,
          DateCreated: new Date().getTime(),
        };
        await historyController.add([data]);
        dispatch(
          handleActions({
            type: 'UPDATE',
            data: { ...customer.data, Rank: (customer?.data?.Rank ?? 0) + rank },
          }),
        );
      }
    };
  static setUp2FA = async (id: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/2fa/setup', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { Id: id },
    });
    return res;
  };
  static verify2FA = async (id: string, token: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/2fa/verify', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { Id: id, token: token },
    });
    return res;
  };
  static disable2FA = async (id: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/2fa/disable', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { Id: id },
    });
    return res;
  };
  static verify2Action = async (id: string, token: string) => {
    const res = await BaseDA.post(ConfigAPI.url + 'data/2fa/protected-action', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { Id: id, token: token },
    });
    return res;
  };
  static createWallet = async (user: string) => {
    const res = await BaseDA.post(ConfigAPI.urlBlockchain + 'auth/login', {
      headers: { module: 'Customer', pid: ConfigAPI.pid },
      body: { username: ConfigAPI.username_blc, password: ConfigAPI.password_blc },
    });
    debugger
    if (res) {
      // create wallet
      const resCreate = await BaseDA.post(ConfigAPI.urlBlockchain + 'wallet/create', {
        headers: {Authorization: `Bearer ${res.token}`},
        body: {
          name: `Chainivo Wallet ${user}`,
          description: 'Wallet for Chainivo',
        },
      });
      debugger
      if (resCreate) {
        return resCreate;
      } else {
        return resCreate;
      }
    } else {
      return res;
    }
  };
}
