import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {FavoriteProduct} from '../models/favoriteProduct';

const fetchFavoriteProducts = createAsyncThunk<
  FavoriteProduct[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('favoriteProduct/fetchFavoriteProducts', async (config, thunkAPI: any) => {
  const controller = new DataController('ProductFavorite');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    console.log('🚀 ~ > ~ res:', res);
    return [];
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchFavoriteProducts};
