import React, {useState} from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  ImageBackground,
} from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';

import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {Winicon, ComponentStatus, showSnackbar} from 'wini-mobile-components';
import QRCode from 'react-native-qrcode-svg';
import LinearGradient from 'react-native-linear-gradient';
import Clipboard from '@react-native-clipboard/clipboard';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {navigate, RootScreen} from '../../router/router';
import {DataController} from '../../base/baseController';
import {LoadingUI} from '../../features/loading';
import {Ultis} from '../../utils/Utils';
import {useNavigation} from '@react-navigation/native';
import {TransactionType} from '../../Config/Contanst';
import {InforHeader} from '../Layout/headers/inforHeader';

const {width} = Dimensions.get('window');

const MyWalletProfile = () => {
  const customer = useSelectorCustomerState().data;
  const [transactionHistory, settransactionHistory] = useState<any[]>([]);
  // Mock data - thay thế bằng data thực từ API
  const [walletBalance, setWalletBalance] = useState(10000000);
  const [incomeAmount, setIncomeAmount] = useState(10000000);
  const [expenseAmount, setExpenseAmount] = useState(10000000);
  const navigation = useNavigation<any>();
  const walletId =
    customer?.WalletAddress && customer.WalletAddress.trim() !== ''
      ? customer.WalletAddress
      : 'e12eased3413da-sd3123-1231412-kuaj';
  const [loading, setLoading] = useState(false);
  React.useEffect(() => {
    debugger;
    
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    // Gọi API để lấy dữ liệu ví
    const controller = new DataController('HistoryReward');
    const res = await controller.getListSimple({
      page: 1,
      size: 5,
      query: `@CustomerId: {${customer.Id}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'ASC'},
    });
    if (res.code === 200) {
      const data = res.data.map((item: any) => {
        const date = Ultis.numberToTime(item.DateCreated, true);
        return {
          id: item.Id,
          type: item.Value > 0 ? 'income' : 'expense',
          amount: item.Value,
          description: item.Description,
          time: date,
        };
      });
      settransactionHistory(data);
      setWalletBalance(
        res.data.reduce((total: number, item: any) => total + item.Value, 0) ??
          0,
      );
      setIncomeAmount(
        res.data
          .filter((item: any) => item.Value > 0)
          .reduce((total: number, item: any) => total + item.Value, 0) ?? 0,
      );
      setExpenseAmount(
        res.data
          .filter((item: any) => item.Value < 0)
          .reduce((total: number, item: any) => total + item.Value, 0) ?? 0,
      );
    }
    setLoading(false);
  };

  const copyToClipboard = () => {
    Clipboard.setString(walletId);
    showSnackbar({
      status: ComponentStatus.SUCCSESS,
      message: 'Đã sao chép ID wallet',
    });
  };
  const navigateToTransactionHistory = () => {
    navigate(RootScreen.TransactionHistory);
  };
  //focus effect
  React.useEffect(() => {
    //lắng nghe sự kiện focus
    const unsubscribe = navigation.addListener('focus', async () => {
      const controller = new DataController('HistoryReward');
      const res = await controller.getListSimple({
        page: 1,
        size: 5,
        query: `@CustomerId: {${customer.Id}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'ASC'},
      });
      if (res.code === 200) {
        const data = res.data.map((item: any) => {
          const date = Ultis.numberToTime(item.DateCreated, true);
          return {
            id: item.Id,
            type: item.Value > 0 ? 'income' : 'expense',
            amount: item.Value,
            description: item.Description,
            time: date,
          };
        });
        settransactionHistory(data);
        setWalletBalance(
          res.data.reduce(
            (total: number, item: any) => total + item.Value,
            0,
          ) ?? 0,
        );
        setIncomeAmount(
          res.data
            .filter((item: any) => item.Value > 0)
            .reduce((total: number, item: any) => total + item.Value, 0) ?? 0,
        );
        setExpenseAmount(
          res.data
            .filter((item: any) => item.Value < 0)
            .reduce((total: number, item: any) => total + item.Value, 0) ?? 0,
        );
      }
    });
    return unsubscribe;
  }, [navigation]);

  const renderTransactionItem = ({item}: any) => (
    <View style={styles.transactionItem}>
      <View style={[styles.transactionIcon, {backgroundColor: '#141316'}]}>
        <Winicon
          src={
            item.type === 'income'
              ? 'outline/arrows/arrow-bottom-left'
              : 'fill/arrows/arrow-top-right'
          }
          size={12}
          color={
            item.type === 'income'
              ? ColorThemes.light.success_main_color
              : ColorThemes.light.error_main_color
          }
          style={{
            backgroundColor:
              item.type === 'income'
                ? ColorThemes.light.success_background
                : ColorThemes.light.warning_border_color,
            padding: 12,
            borderRadius: 4,
          }}
        />
      </View>
      <View style={styles.transactionContent}>
        <View style={styles.transactionHeader}>
          <Text
            style={[
              styles.transactionAmount,
              {
                color:
                  item.type === 'income'
                    ? ColorThemes.light.secondary2_main_color
                    : ColorThemes.light.error_main_color,
              },
            ]}>
            {item.type === 'income' ? '+' : ''}
            {Ultis.money(item.amount)} GCT
          </Text>
        </View>
        {item.description && (
          <Text style={styles.transactionDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        <Text style={styles.transactionTime}>{item.time}</Text>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <InforHeader title={'Ví của tôi'} />

      {loading ? (
        <LoadingUI isLoading={loading} />
      ) : (
        <>
          {/* QR Code Section */}
          <View style={styles.qrSection}>
            <View style={styles.qrContainer}>
              <ImageBackground
                source={require('../../assets/bg-qr.png')}
                style={styles.qrGradient}>
                <View style={styles.qrCodeWrapper}>
                  {walletId && walletId.trim() !== '' ? (
                    <QRCode
                      value={walletId}
                      size={110}
                      backgroundColor="white"
                      color="black"
                    />
                  ) : (
                    <View style={{width: 110, height: 110, backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center'}}>
                      <Text style={{color: '#666', fontSize: 12}}>No QR Data</Text>
                    </View>
                  )}
                </View>
              </ImageBackground>
            </View>

            <View style={styles.walletInfo}>
              <Text style={styles.walletIdLabel}>ID wallet</Text>
              <Text style={styles.walletIdText} numberOfLines={2}>
                {walletId}
              </Text>

              <View style={styles.actionButtons}>
                <TouchableOpacity onPress={copyToClipboard}>
                  <LinearGradient
                    start={{x: 0, y: 0}}
                    end={{x: 1, y: 0}}
                    colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                    style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>Sao chép</Text>
                  </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() =>
                    navigate(RootScreen.TransferCANPoint, {
                      Type: TransactionType.tranfer,
                    })
                  }>
                  <LinearGradient
                    start={{x: 0, y: 0}}
                    end={{x: 1, y: 0}}
                    colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                    style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>
                      Chuyển CAN nội bộ
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() =>
                    navigate(RootScreen.TransferCANPoint, {
                      Type: TransactionType.Withdraw,
                    })
                  }>
                  <LinearGradient
                    start={{x: 0, y: 0}}
                    end={{x: 1, y: 0}}
                    colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                    style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>Rút CAN point</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Balance Section */}
          <View style={styles.balanceSection}>
            <Text style={styles.balanceLabel}>Số dư ví</Text>
            <Text style={styles.balanceAmount}>
              {Ultis.money(walletBalance)}
            </Text>
          </View>

          {/* Income/Expense Cards */}
          <View style={styles.statsSection}>
            <View style={[styles.statsCard]}>
              <Winicon
                src="outline/arrows/arrow-bottom-left"
                size={20}
                color={'#8EDFEB'}
              />
              <Text style={[styles.statsLabel, {color: '#8EDFEB'}]}>
                Income
              </Text>
              <Text style={[styles.statsAmount, {color: '#8EDFEB'}]}>
                {Ultis.money(incomeAmount)}
              </Text>
            </View>

            <View style={[styles.statsCard]}>
              <Winicon
                src="fill/arrows/arrow-top-right"
                size={20}
                color="#5166BF"
              />
              <Text style={[styles.statsLabel, {color: '#5166BF'}]}>
                Expense
              </Text>
              <Text style={[styles.statsAmount, {color: '#5166BF'}]}>
                {Ultis.money(expenseAmount)}
              </Text>
            </View>
          </View>

          {/* Transaction History */}
          {transactionHistory.length > 0 ? (
            <>
              <View style={styles.historySection}>
                <View style={styles.historyHeader}>
                  <Text style={styles.historyTitle}>Lịch sử giao dịch</Text>
                  <TouchableOpacity onPress={navigateToTransactionHistory}>
                    <Text style={styles.viewAllText}>Xem tất cả</Text>
                  </TouchableOpacity>
                </View>

                <FlatList
                  data={transactionHistory}
                  renderItem={renderTransactionItem}
                  keyExtractor={item => item.id.toString()}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              </View>
            </>
          ) : null}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  qrSection: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: 'white',
    marginBottom: 10,
    alignItems: 'center',
    width: '100%',
  },
  qrContainer: {
    marginRight: 15,
    width: '50%',
  },
  qrGradient: {
    width: '100%',
    height: 200,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrCodeWrapper: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
  },
  walletInfo: {
    flex: 1,
  },
  walletIdLabel: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  walletIdText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
    marginBottom: 12,
  },
  actionButtons: {
    gap: 0,
  },
  actionButton: {
    // paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
    marginBottom: 4,
    width: '100%',
  },
  actionButtonText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_main_reverse_background_color,
    fontWeight: '500',
    textAlign: 'center',
  },
  balanceSection: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  balanceLabel: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ColorThemes.light.primary_main_color,
  },
  statsSection: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 10,
    width: '100%',
  },
  statsCard: {
    flex: 1,
    // paddingHorizontal: 12,
    paddingVertical: 24,
    borderRadius: 25,
    alignItems: 'center',
    width: '40%',
    shadowColor: '#1890FF4D',
    backgroundColor: ColorThemes.light.white,
    shadowOffset: {
      width: 1,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  statsLabel: {
    fontSize: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 4,
    marginBottom: 4,
    textAlign: 'center',
    fontWeight: '500',
  },
  statsAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  historySection: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
  },
  viewAllText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  transactionItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionContent: {
    flex: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  transactionDescription: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
    lineHeight: 20,
  },
  transactionTime: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default MyWalletProfile;
