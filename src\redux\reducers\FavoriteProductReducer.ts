import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {Category} from '../models/category';
import {fetchCategories} from '../actions/categoryAction';
import {fetchFavoriteProducts} from '../actions/favoriteProductAction';
import {FavoriteProduct} from '../models/favoriteProduct';

interface FavoriteProductStoreState {
  data: FavoriteProduct[];
  loading: boolean;
}

export type {FavoriteProductStoreState};

const initState: FavoriteProductStoreState = {
  data: [],
  loading: false,
};

export const favoriteProductSlice = createSlice({
  name: 'favoriteProduct',
  initialState: initState,
  reducers: {
    setData: <K extends keyof FavoriteProductStoreState>(
      state: FavoriteProductStoreState,
      action: PayloadAction<{
        stateName: K;
        data: FavoriteProductStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: (
      state: FavoriteProductStoreState,
      action: PayloadAction<boolean>,
    ) => {
      state.loading = action.payload;
    },
    onFetchDone: (state: FavoriteProductStoreState) => {
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchFavoriteProducts.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchFavoriteProducts.fulfilled, (state, action) => {
      state.loading = false;
      state.data = action.payload;
    });
    builder.addCase(fetchFavoriteProducts.rejected, state => {
      state.loading = false;
    });
  },
});

export const {setData, onFetching, onFetchDone} = favoriteProductSlice.actions;

export default favoriteProductSlice.reducer;
