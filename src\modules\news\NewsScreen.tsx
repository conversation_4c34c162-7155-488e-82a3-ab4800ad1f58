import {View} from 'react-native';
import ScreenHeader from '../../Screen/Layout/header';
import React, {useCallback, useState} from 'react';
import ScrollableTabs, {TabId} from './scrollable/ScrollableTabs';
import TabNews from './components/TabNews';
import TabEvents from './components/TabEvents';
import HomeHeader from '../../Screen/Layout/headers/HomeHeader';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';

const NewsScreen = () => {
  const [tab, setTab] = useState<TabId>('news');
  const onChangeTab = useCallback((tab: TabId) => {
    setTab(tab);
  }, []);
  return (
    <View style={{flex: 1}}>
      <InforHeader title={'Tin tức'} />

      <View style={{flex: 1}}>
        <ScrollableTabs onChangeTab={onChangeTab} />

        {tab === 'news' && <TabNews />}
        {tab === 'news-event' && <TabEvents />}
      </View>
    </View>
  );
};

export default NewsScreen;
