import React, {useEffect} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppSvg, FDialog} from 'wini-mobile-components';
import {navigate, RootScreen} from '../../../router/router';
import CartIcon from '../../../components/CartIcon';
import iconSvg from '../../../svg/icon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import HeaderBackground from '../../../components/shop/HeaderShop';
import {dialogCheckAcc} from '../mainLayout';
import {useCategoryHook} from '../../../redux/hook/categoryHook';

const HeaderLogo = () => {
  const categoryHook = useCategoryHook();
  // Thiết lập StatusBar khi component mount
  useEffect(() => {
    // Đảm bảo StatusBar trong suốt và kịch lên đỉnh màn hình
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    // Trả về hàm cleanup để reset StatusBar khi unmount
    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('#FFA500');
      }
    };
  }, []);

  const handleMenuPress = () => {
    categoryHook.setData('showDrawer', true);
  };

  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>

      <SafeAreaView style={styles.headerContent}>
        <View style={styles.topRow}>
          <TouchableOpacity onPress={handleMenuPress}>
            <AppSvg SvgSrc={iconSvg.menuUnfold} size={25} />
          </TouchableOpacity>
          <AppSvg
            SvgSrc={iconSvg.logoText}
            size={170}
            style={{marginLeft: 10}}
          />
        </View>
        <View>
          <View style={styles.rightIcons}>
            <Image
              source={require('../../../assets/images/logo.png')}
              style={{width: 30, height: 30, borderRadius: 20}}
            />

            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.NewsScreen);
              }}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.notification} size={16} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.CartPage);
              }}>
              <View style={styles.iconCircle}>
                <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  headerContent: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
    paddingHorizontal: 12,
  },
  topRow: {
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
});

export default HeaderLogo;
