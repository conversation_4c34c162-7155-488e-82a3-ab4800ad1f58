import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { <PERSON>L<PERSON>, ScrollView } from 'react-native-gesture-handler';
import { ComponentStatus, showSnackbar, Winicon } from 'wini-mobile-components';
import { Title } from '../../../Config/Contanst';
import { OrderData } from '../../../mock/shopData';
import CardOrder from '../card/CardOrder';
import { navigate, RootScreen } from '../../../router/router';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch } from 'react-redux';
import { OrderActions } from '../../../redux/reducers/OrderReducer';
import { useSelectorOrderState } from '../../../redux/hook/orderHook ';
import { DataController } from '../../../base/baseController';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import EmptyPage from '../../../Screen/emptyPage';

interface OrderCardProps {
    type: string;
    setTypeCard: (type: string) => void;
    dataSearchResult: any[];
    dataSearch: string;
}
const ListCard = (props: OrderCardProps) => {
    const { type, setTypeCard, dataSearchResult, dataSearch } = props
    const navigation = useNavigation<any>();
    const [data, setData] = useState<any[]>()
    const [action, setAction] = useState<string>("")
    const [loading, setLoading] = useState<boolean>(false)
    const orderInfo = useSelectorOrderState().data;
    const shopController = new DataController('Order');
    const dispatch = useDispatch<any>();
    const shopInfo = useSelectorShopState().data;

    useEffect(() => {
        if (type === Title.New) {
            setAction("Xác nhận đơn")
            setData(orderInfo?.NewOrder?.data)
        } else if (type === Title.Processing) {
            setAction("Cập nhật trạng thái")
            setData(orderInfo?.ProcessOrder?.data)
        } else if (type === Title.Cancel) {
            setAction(type)
            setData(orderInfo?.CancelOrder?.data)
        } else if (type === Title.Done) {
            setAction("Đánh giá")
            setData(orderInfo?.DoneOrder?.data)
        }
    }, [type, action, orderInfo])

    const handleUpdateStatusProcessOrder = async (item: any) => {
        console.log("check-action", action)
        if (action == "Xác nhận đơn") {
            await shopController.edit([{ ...item, Status: 2 }]).then((res: any) => {
                if (res.code == 200) {
                    showSnackbar({
                        message: "Cập nhật trạng thái đơn hàng sang trạng thái đang xử lý thành công",
                        status: ComponentStatus.SUCCSESS
                    });
                    dispatch(OrderActions.getInforOrder(shopInfo[0].Id))
                }
            })
        }
        if (action == "Cập nhật trạng thái") {
            await shopController.edit([{ ...item, Status: 3 }]).then((res: any) => {
                if (res.code == 200) {
                    showSnackbar({
                        message: "Cập nhật trạng thái đơn hàng sang trạng thái hoàn thành thành công",
                        status: ComponentStatus.SUCCSESS
                    });
                    dispatch(OrderActions.getInforOrder(shopInfo[0].Id))
                }
            })
        }
        if (action == "Đánh giá") {
            navigation.navigate(RootScreen.CreateReviewOrder, {
                Data: item,
            });
        }
    }
    const handleUpdateStatusProcessOrderCancel = async (item: any) => {
        await shopController.edit([{ ...item, Status: 4 }]).then((res: any) => {
            if (res.code == 200) {
                showSnackbar({
                    message: "Cập nhật trạng thái đơn hàng sang trạng thái hủy thành công",
                    status: ComponentStatus.SUCCSESS
                });
                dispatch(OrderActions.getInforOrder(shopInfo[0].Id))
            }
        })
    }

    if (data && data.length > 0 || dataSearchResult && dataSearchResult.length > 0) {
        return (
            <FlatList
                data={dataSearch ? dataSearchResult : data}
                style={{ flex: 1 }}
                keyExtractor={(item, i) => `${i} ${item.id}`}
                renderItem={({ item, index }) => (
                    CardOrder({ item, index }, action, loading, handleUpdateStatusProcessOrder, handleUpdateStatusProcessOrderCancel))
                }
            />
        );
    } else {
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                <EmptyPage />
            </View>
        )
    }
};

export default ListCard;